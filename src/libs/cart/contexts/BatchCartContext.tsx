import {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from 'react';
import { OfferType } from '@/types';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';

export interface BatchCartItem {
  offerId: string;
  offer: OfferType;
  quantity: number;
}

export interface BatchCartContextType {
  items: Record<string, BatchCartItem>;
  addOrUpdateItem: (offer: OfferType, quantity: number) => void;
  removeItem: (offerId: string) => void;
  updateQuantity: (offerId: string, quantity: number) => void;
  getItemQuantity: (offerId: string) => number;
  getTotalItemCount: () => number;
  getSubtotal: () => number;
  clearItems: () => void;
  addAllToCart: (
    onSuccess?: () => void,
    onError?: (message: string) => void,
  ) => Promise<void>;
  isAddingToCart: boolean;
}

const BatchCartContext = createContext<BatchCartContextType | null>(null);

export function useBatchCartContext() {
  const context = useContext(BatchCartContext);
  if (!context) {
    throw new Error(
      'BatchCart components must be used within a BatchCartProvider',
    );
  }
  return context;
}

interface BatchCartProviderProps {
  children: ReactNode;
}

export function BatchCartProvider({ children }: BatchCartProviderProps) {
  const [items, setItems] = useState<Record<string, BatchCartItem>>({});
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const { addToCart } = useCartStore();

  const addOrUpdateItem = useCallback((offer: OfferType, quantity: number) => {
    if (quantity <= 0) {
      setItems((prev) => {
        const newItems = { ...prev };
        delete newItems[offer.id];
        return newItems;
      });
      return;
    }

    setItems((prev) => ({
      ...prev,
      [offer.id]: {
        offerId: offer.id,
        offer,
        quantity,
      },
    }));
  }, []);

  const removeItem = useCallback((offerId: string) => {
    setItems((prev) => {
      const newItems = { ...prev };
      delete newItems[offerId];
      return newItems;
    });
  }, []);

  const updateQuantity = useCallback(
    (offerId: string, quantity: number) => {
      if (quantity <= 0) {
        removeItem(offerId);
        return;
      }

      setItems((prev) => {
        const item = prev[offerId];
        if (!item) return prev;

        return {
          ...prev,
          [offerId]: {
            ...item,
            quantity,
          },
        };
      });
    },
    [removeItem],
  );

  const getItemQuantity = useCallback(
    (offerId: string) => {
      return items[offerId]?.quantity ?? 0;
    },
    [items],
  );

  const getTotalItemCount = useCallback(() => {
    return Object.values(items).reduce(
      (total, item) => total + item.quantity,
      0,
    );
  }, [items]);

  const getSubtotal = useCallback(() => {
    return Object.values(items).reduce((total, item) => {
      const { salePrice } = getProductOfferComputedData(item.offer);
      return total + salePrice * item.quantity;
    }, 0);
  }, [items]);

  const clearItems = useCallback(() => {
    setItems({});
  }, []);

  const addSingleItemToCart = useCallback(
    (productOfferId: string, quantity: number): Promise<void> => {
      return new Promise((resolve, reject) => {
        let resolved = false;

        addToCart({
          productOfferId,
          quantity,
          onError: (message) => {
            if (!resolved) {
              resolved = true;
              reject(new Error(message));
            }
          },
        });

        // Since addToCart doesn't provide a success callback, we monitor the updating state
        const checkCompletion = () => {
          const { updatingProductIds } = useCartStore.getState();
          if (!updatingProductIds.has(productOfferId)) {
            if (!resolved) {
              resolved = true;
              resolve();
            }
          } else {
            setTimeout(checkCompletion, 100);
          }
        };

        // Start checking after a small delay to allow the update to begin
        setTimeout(checkCompletion, 100);
      });
    },
    [addToCart],
  );

  const addAllToCart = useCallback(
    async (onSuccess?: () => void, onError?: (message: string) => void) => {
      const itemsArray = Object.values(items);
      if (itemsArray.length === 0) {
        onError?.('No items to add to cart');
        return;
      }

      setIsAddingToCart(true);

      try {
        // Add items sequentially to avoid race conditions
        for (const item of itemsArray) {
          await addSingleItemToCart(item.offerId, item.quantity);
        }

        clearItems();
        onSuccess?.();
      } catch (error) {
        const message =
          error instanceof Error
            ? error.message
            : 'Failed to add items to cart';
        onError?.(message);
      } finally {
        setIsAddingToCart(false);
      }
    },
    [items, addSingleItemToCart, clearItems],
  );

  const contextValue: BatchCartContextType = {
    items,
    addOrUpdateItem,
    removeItem,
    updateQuantity,
    getItemQuantity,
    getTotalItemCount,
    getSubtotal,
    clearItems,
    addAllToCart,
    isAddingToCart,
  };

  return (
    <BatchCartContext.Provider value={contextValue}>
      {children}
    </BatchCartContext.Provider>
  );
}
