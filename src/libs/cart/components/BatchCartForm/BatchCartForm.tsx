import { FormEventHandler, useCallback, useState } from 'react';
import { Flex } from '@/libs/ui/Flex/Flex';
import { Button } from '@/libs/ui/Button/Button';
import { OfferType } from '@/types';
import { useBatchCart } from '../../hooks/useBatchCart';
import {
  AddToCartInput,
  type AddToCartInputProps,
} from '@/libs/products/components/AddToCartInput/AddToCartInput';

interface BatchCartFormProps {
  offer: OfferType;
  increments: number;
}

export const BatchCartForm = ({ offer, increments }: BatchCartFormProps) => {
  const {
    addOffer,
    getOfferQuantity,
    getOfferQuantityInMainCart,
    isOfferInBatchCart,
  } = useBatchCart();

  const [amount, setAmount] = useState(increments ?? 1);

  const currentBatchQuantity = getOfferQuantity(offer.id);
  const mainCartQuantity = getOfferQuantityInMainCart(offer.id);
  const isInBatchCart = isOfferInBatchCart(offer.id);

  const handleQuantityUpdate: AddToCartInputProps['onUpdate'] = ({
    amount: newAmount,
  }) => {
    setAmount(newAmount);
  };

  const handleAddToBatchClick: FormEventHandler<HTMLFormElement> = useCallback(
    (event) => {
      event.preventDefault();
      addOffer(offer, amount);
    },
    [offer, addOffer, amount],
  );

  return (
    <form onSubmit={handleAddToBatchClick}>
      <Flex gap="12px" flex="grow">
        <AddToCartInput
          originalAmount={increments}
          minIncrement={increments}
          onUpdate={handleQuantityUpdate}
        />
        <Button
          type="submit"
          variant={isInBatchCart ? 'filled' : 'outline'}
          fullWidth
        >
          {isInBatchCart ? `Update (${currentBatchQuantity})` : 'Add to Batch'}
          {mainCartQuantity > 0 && ` (${mainCartQuantity} in cart)`}
        </Button>
      </Flex>
    </form>
  );
};
