import { useBatchCartContext } from '../contexts/BatchCartContext';
import { OfferType } from '@/types';
import { useCartProductMapState } from './useCartProductMapState';

/**
 * Hook that provides convenient methods for managing batch cart operations
 */
export function useBatchCart() {
  const context = useBatchCartContext();
  const cartProductMapState = useCartProductMapState();

  /**
   * Add an offer to the batch cart with the specified quantity
   */
  const addOffer = (offer: OfferType, quantity: number) => {
    context.addOrUpdateItem(offer, quantity);
  };

  /**
   * Remove an offer from the batch cart
   */
  const removeOffer = (offerId: string) => {
    context.removeItem(offerId);
  };

  /**
   * Update the quantity of an offer in the batch cart
   */
  const updateOfferQuantity = (offerId: string, quantity: number) => {
    context.updateQuantity(offerId, quantity);
  };

  /**
   * Get the current quantity of an offer in the batch cart
   */
  const getOfferQuantity = (offerId: string) => {
    return context.getItemQuantity(offerId);
  };

  /**
   * Get the current quantity of an offer in the main cart
   */
  const getOfferQuantityInMainCart = (offerId: string) => {
    return cartProductMapState[offerId]?.quantity ?? 0;
  };

  /**
   * Check if an offer is in the batch cart
   */
  const isOfferInBatchCart = (offerId: string) => {
    return context.getItemQuantity(offerId) > 0;
  };

  /**
   * Get all offers in the batch cart as an array
   */
  const getBatchCartItems = () => {
    return Object.values(context.items);
  };

  /**
   * Get the total number of items across all offers in the batch cart
   */
  const getTotalItemCount = () => {
    return context.getTotalItemCount();
  };

  /**
   * Get the subtotal for all items in the batch cart
   */
  const getSubtotal = () => {
    return context.getSubtotal();
  };

  /**
   * Clear all items from the batch cart
   */
  const clearBatchCart = () => {
    context.clearItems();
  };

  /**
   * Add all items from the batch cart to the main cart
   */
  const addAllToMainCart = async (
    onSuccess?: () => void,
    onError?: (message: string) => void
  ) => {
    await context.addAllToCart(onSuccess, onError);
  };

  /**
   * Check if items are currently being added to the main cart
   */
  const isAddingToCart = context.isAddingToCart;

  /**
   * Check if the batch cart is empty
   */
  const isEmpty = getTotalItemCount() === 0;

  return {
    // State
    items: context.items,
    isEmpty,
    isAddingToCart,
    
    // Actions
    addOffer,
    removeOffer,
    updateOfferQuantity,
    clearBatchCart,
    addAllToMainCart,
    
    // Getters
    getOfferQuantity,
    getOfferQuantityInMainCart,
    isOfferInBatchCart,
    getBatchCartItems,
    getTotalItemCount,
    getSubtotal,
  };
}
