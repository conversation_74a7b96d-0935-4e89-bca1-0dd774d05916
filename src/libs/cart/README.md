# Batch Cart System

A React Context-based system for managing multiple offers in a batch before adding them to the main cart. This is useful for promotional scenarios, bulk ordering, or any situation where users need to select multiple items before committing to add them to their cart.

## Key Features

- **Batch Management**: Add multiple offers with quantities before committing to the main cart
- **State Management**: Track quantities, prices, and offer details for each item
- **Subtotal Calculation**: Automatically calculate subtotals across all promotional offers
- **Integration**: Seamlessly integrates with the existing cart system
- **Type Safety**: Full TypeScript support with proper type definitions

## Architecture

### Core Components

1. **BatchCartContext**: React Context that manages the batch cart state
2. **useBatchCart**: Convenient hook for interacting with the batch cart
3. **BatchCartForm**: Component for adding individual offers to the batch
4. **BatchCartSummary**: Component for displaying batch contents and adding all to main cart

### Key Differences from AddToCartForm

- **Multiple Offers**: Handles multiple offers simultaneously vs. single offer
- **Batch Operations**: Collects items before adding to main cart vs. immediate addition
- **State Management**: Uses React Context vs. direct cart store interaction
- **Use Cases**: Ideal for promotions, bulk orders, or curated selections

## Usage

### 1. Setup the Provider

Wrap your component tree with the `BatchCartProvider`:

```tsx
import { BatchCartProvider } from '@/libs/cart/contexts';

function App() {
  return (
    <BatchCartProvider>
      {/* Your app components */}
    </BatchCartProvider>
  );
}
```

### 2. Use the Hook

Use the `useBatchCart` hook to interact with the batch cart:

```tsx
import { useBatchCart } from '@/libs/cart/hooks/useBatchCart';

function MyComponent() {
  const {
    addOffer,
    removeOffer,
    getTotalItemCount,
    getSubtotal,
    addAllToMainCart,
    isEmpty,
  } = useBatchCart();

  // Add an offer to the batch
  const handleAddOffer = (offer: OfferType, quantity: number) => {
    addOffer(offer, quantity);
  };

  // Add all batch items to main cart
  const handleAddAllToCart = async () => {
    await addAllToMainCart(
      () => console.log('Success!'),
      (error) => console.error('Error:', error)
    );
  };

  return (
    <div>
      <p>Items in batch: {getTotalItemCount()}</p>
      <p>Subtotal: ${getSubtotal().toFixed(2)}</p>
      <button onClick={handleAddAllToCart} disabled={isEmpty}>
        Add All to Cart
      </button>
    </div>
  );
}
```

### 3. Use the Components

#### BatchCartForm

For adding individual offers to the batch:

```tsx
import { BatchCartForm } from '@/libs/cart/components';

function ProductCard({ offer }: { offer: OfferType }) {
  return (
    <div>
      <h3>{offer.name}</h3>
      <BatchCartForm offer={offer} increments={offer.increments} />
    </div>
  );
}
```

#### BatchCartSummary

For displaying batch contents and batch operations:

```tsx
import { BatchCartSummary } from '@/libs/cart/components';

function Sidebar() {
  return (
    <div>
      <BatchCartSummary 
        onSuccess={() => alert('Added to cart!')}
        onError={(error) => alert(`Error: ${error}`)}
      />
    </div>
  );
}
```

## API Reference

### useBatchCart Hook

#### State
- `items`: Record of all items in the batch cart
- `isEmpty`: Boolean indicating if batch cart is empty
- `isAddingToCart`: Boolean indicating if items are being added to main cart

#### Actions
- `addOffer(offer, quantity)`: Add or update an offer in the batch
- `removeOffer(offerId)`: Remove an offer from the batch
- `updateOfferQuantity(offerId, quantity)`: Update quantity of an offer
- `clearBatchCart()`: Clear all items from the batch
- `addAllToMainCart(onSuccess?, onError?)`: Add all batch items to main cart

#### Getters
- `getOfferQuantity(offerId)`: Get quantity of offer in batch cart
- `getOfferQuantityInMainCart(offerId)`: Get quantity of offer in main cart
- `isOfferInBatchCart(offerId)`: Check if offer is in batch cart
- `getBatchCartItems()`: Get all batch cart items as array
- `getTotalItemCount()`: Get total item count across all offers
- `getSubtotal()`: Get subtotal for all batch items

### BatchCartContextType

```tsx
interface BatchCartContextType {
  items: Record<string, BatchCartItem>;
  addOrUpdateItem: (offer: OfferType, quantity: number) => void;
  removeItem: (offerId: string) => void;
  updateQuantity: (offerId: string, quantity: number) => void;
  getItemQuantity: (offerId: string) => number;
  getTotalItemCount: () => number;
  getSubtotal: () => number;
  clearItems: () => void;
  addAllToCart: (onSuccess?: () => void, onError?: (message: string) => void) => Promise<void>;
  isAddingToCart: boolean;
}
```

## Example Implementation

See `src/libs/cart/examples/BatchCartExample.tsx` for a complete working example.

## Integration Notes

- The batch cart integrates with the existing `useCartStore` for adding items to the main cart
- Price calculations use the existing `getProductOfferComputedData` utility
- The system respects offer increments and pricing rules
- Error handling follows the same patterns as the existing cart system
